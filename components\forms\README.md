# Form Components

This directory contains reusable form components built with React Hook Form and Zod validation.

## Components

### Form

A container component for forms with built-in keyboard avoiding view and scrolling.

```tsx
<Form>
  {/* Form fields go here */}
</Form>
```

### FormInput

A text input component with validation.

```tsx
<FormInput
  name="fieldName"
  control={control}
  label="Field Label"
  placeholder="Enter value"
  // Optional props
  multiline={false}
  numberOfLines={1}
  rules={{ required: "This field is required" }}
/>
```

### FormRadioGroup

A radio button group component.

```tsx
<FormRadioGroup
  name="fieldName"
  control={control}
  label="Field Label"
  options={[
    { label: "Option 1", value: "option1" },
    { label: "Option 2", value: "option2" },
  ]}
  // Optional props
  horizontal={true}
/>
```

### FormDatePicker

A date picker component with optional range selection.

```tsx
// Single date
<FormDatePicker
  name="date"
  control={control}
  label="Date"
  placeholder="Select date"
  minDate="2023-01-01"
/>

// Date range
<FormDatePicker
  name="startDate"
  control={control}
  label="Date Range"
  placeholder="Select date range"
  isRange={true}
  endDateName="endDate"
  minDate="2023-01-01"
/>
```

### FormSubmitButton

A submit button component with optional cancel button.

```tsx
<FormSubmitButton
  submitLabel="Submit"
  cancelLabel="Cancel"
  onSubmit={handleSubmit(onSubmit)}
  onCancel={handleCancel}
  isSubmitting={isSubmitting}
  isValid={isValid}
  submitIcon="paper-plane-outline"
  cancelIcon="close-outline"
/>
```

## Usage with Zod

1. Define your schema:

```tsx
import { z } from "zod";

export const formSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters"),
  email: z.string().email("Invalid email address"),
  // Add more fields as needed
});

export type FormData = z.infer<typeof formSchema>;
```

2. Use with React Hook Form:

```tsx
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

const {
  control,
  handleSubmit,
  formState: { isValid },
} = useForm<FormData>({
  defaultValues: { name: "", email: "" },
  resolver: zodResolver(formSchema),
  mode: "onChange",
});

const onSubmit = (data: FormData) => {
  // Handle form submission
};
```

3. Render your form:

```tsx
<Form>
  <FormInput name="name" control={control} label="Name" />
  <FormInput name="email" control={control} label="Email" />
  <FormSubmitButton
    submitLabel="Submit"
    onSubmit={handleSubmit(onSubmit)}
    isValid={isValid}
  />
</Form>
```
