import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Alert,
  KeyboardAvoidingViewBase,
  Platform,
  KeyboardAvoidingView,
  ScrollView,
} from "react-native";
import { useRouter } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Colors } from "@/constants/Colors";
import {
  FormInput,
  FormDatePicker,
  FormSubmitButton,
  FormImagePicker,
  FormSelect,
} from "@/components/forms";
import {
  incidentReportSchema,
  IncidentReportFormData,
  defaultIncidentReportValues,
  getPriorityLevelOptions,
} from "@/schemas/incident-reporting";
import { errorToast, successToast } from "@/lib/utils";

export default function IncidentReportingScreen() {
  const router = useRouter();

  // Initialize form with react-hook-form and zod validation
  const {
    control,
    handleSubmit,
    reset,
    formState: { isValid },
  } = useForm<IncidentReportFormData>({
    defaultValues: defaultIncidentReportValues,
    resolver: zodResolver(incidentReportSchema),
    mode: "onChange",
  });

  // No header, so no back button handler needed

  // Handle form submission
  const onSubmit = (data: IncidentReportFormData) => {
    try {
      // Simulate form submission
      console.log("Form submitted successfully:", data);
      successToast("Incident reported successfully!");
      reset();
    } catch (err) {
      errorToast(err);
    }
  };

  return (
    <View style={styles.flex}>
      <StatusBar style="dark" />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        keyboardVerticalOffset={100}
        style={styles.container}
      >
        <ScrollView
          contentContainerStyle={styles.flex}
          showsVerticalScrollIndicator={false}
        >
          {/* Date Input */}
          <FormDatePicker
            name="date"
            control={control}
            label="Date"
            placeholder="dd-mm-yy"
            minDate={
              new Date(new Date().setFullYear(new Date().getFullYear() - 1))
                .toISOString()
                .split("T")[0]
            } // 1 year ago
            maxDate={new Date().toISOString().split("T")[0]} // Today
          />

          {/* Location Input */}
          <FormInput
            name="location"
            control={control}
            label="Location"
            placeholder="Select"
          />

          {/* Description Input */}
          <FormInput
            name="description"
            control={control}
            label="Description"
            placeholder="Type something..."
            multiline
            numberOfLines={4}
          />

          {/* Type Input */}
          <FormInput
            name="type"
            control={control}
            label="Type"
            placeholder="Enter"
          />

          {/* Priority Level Dropdown */}
          <FormSelect
            name="priorityLevel"
            control={control}
            label="Priority Level"
            placeholder="Select"
            options={getPriorityLevelOptions()}
          />

          {/* Upload Evidence */}
          <FormImagePicker
            name="evidence"
            control={control}
            label="Upload Evidence"
          />

          {/* Submit Button */}
          <FormSubmitButton
            submitLabel="Submit"
            onSubmit={handleSubmit(onSubmit)}
            isValid={isValid}
            style={styles.submitButton}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingBottom: 50,
    paddingTop: 16,
    paddingHorizontal: 16,
  },
  submitButton: {
    marginTop: 20,
  },
  flex: { flex: 1 },
});
