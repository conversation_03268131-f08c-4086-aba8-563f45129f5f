import { useState, useEffect } from "react";
import { useRouter } from "expo-router";
import { Alert } from "react-native";
import {
  useClockOutMutation,
  useGetUserAttendanceQuery,
} from "@/generated/graphql";
import { useQueryClient } from "@tanstack/react-query";
import { ClockStatus } from "@/components/attendance/ClockInOutButton";
import { useSession } from "@/providers/auth-provider";

interface UseClockStatusProps {
  shiftId?: string;
  locationId?: string;
}

export function useClockStatus({
  shiftId,
  locationId,
}: UseClockStatusProps = {}) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [clockStatus, setClockStatus] = useState<ClockStatus>({
    isClockedIn: false,
    clockInTime: null,
    attendanceId: null,
  });

  // Get today's attendance to check current status
  const today = new Date();
  const startOfDay = new Date(
    today.getFullYear(),
    today.getMonth(),
    today.getDate()
  );
  const endOfDay = new Date(
    today.getFullYear(),
    today.getMonth(),
    today.getDate(),
    23,
    59,
    59
  );

  const { session } = useSession();

  const { data: attendanceData, refetch: refetchAttendance } =
    useGetUserAttendanceQuery(
      {
        filter: {
          dateRange: {
            startDate: startOfDay,
            endDate: endOfDay,
          },
          userIds: [session!.userId],
        },
      },
      {
        enabled: !!session && !!session.userId,
        initialData: {
          myAttendances: [],
        },
      }
    );

  const { mutateAsync: clockOut, isLoading: isClockingOut } =
    useClockOutMutation({
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["GetUserAttendance"] });
        setClockStatus({
          isClockedIn: false,
          clockInTime: null,
          attendanceId: null,
        });
        Alert.alert("Success", "You have successfully clocked out!");
      },
      onError: (error) => {
        if (error instanceof Error) {
          Alert.alert("Error", error.message);
        }
      },
    });

  // Update clock status based on attendance data
  useEffect(() => {
    if (attendanceData?.myAttendances) {
      // Find today's active attendance (no end time)
      const activeAttendance = attendanceData.myAttendances.find(
        (attendance) => !attendance.endTime
      );

      if (activeAttendance) {
        setClockStatus({
          isClockedIn: true,
          clockInTime: new Date(activeAttendance.startTime),
          attendanceId: activeAttendance.id,
        });
      } else {
        setClockStatus({
          isClockedIn: false,
          clockInTime: null,
          attendanceId: null,
        });
      }
    }
  }, [attendanceData]);

  const handleClockIn = () => {
    if (!shiftId || !locationId) {
      Alert.alert(
        "Error",
        "Shift and location information is required to clock in."
      );
      return;
    }

    // Navigate to face scanning screen
    router.push({
      pathname: "/shifts/clock-in-scan",
      params: { shiftId, locationId },
    });
  };

  const handleClockOut = async () => {
    if (!clockStatus.attendanceId) {
      Alert.alert("Error", "No active attendance found.");
      return;
    }

    Alert.alert("Confirm Clock Out", "Are you sure you want to clock out?", [
      {
        text: "Cancel",
        style: "cancel",
      },
      {
        text: "Clock Out",
        style: "destructive",
        onPress: async () => {
          try {
            // For now, we'll use a placeholder image since clock out might not require face scan
            // This can be enhanced later to include face scanning for clock out as well
            await clockOut({
              clockOut: {
                attendanceId: clockStatus.attendanceId!,
              },
            });
          } catch (error) {
            console.error("Clock out failed:", error);
          }
        },
      },
    ]);
  };

  const refreshStatus = () => {
    refetchAttendance();
  };

  return {
    clockStatus,
    handleClockIn,
    handleClockOut,
    refreshStatus,
    isLoading: isClockingOut,
  };
}
