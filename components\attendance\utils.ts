import {
  eachDayOfInterval,
  startOfMonth,
  endOfMonth,
  format,
  isAfter,
  isBefore,
  parseISO,
  differenceInMinutes,
} from "date-fns";
import {
  AttendanceStatus,
  DailyAttendance,
  AttendanceSummary,
} from "./AttendanceOverview";

// Type for the GraphQL attendance data (matching the generated types)
export interface GraphQLAttendance {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  date: Date;
  endTime?: Date | null;
  overTime?: Date | null;
  overTimeSpentInMinutes: number;
  startTime: Date;
  timeSpentInMinutes: number;
  actualClockInTime: Date;
  actualClockOutTime?: Date | null;
  shift?: {
    startDateTime: Date;
    endDateTime: Date;
  } | null;
  location?: {
    id: string;
    name: string;
  } | null;
}

/**
 * Determines attendance status based on actual clock-in time vs scheduled shift time
 */
function determineAttendanceStatus(
  attendance: GraphQLAttendance,
  currentDate: Date
): AttendanceStatus {
  const attendanceDate = new Date(attendance.date);
  const today = new Date();

  // If the date is in the future, mark as FUTURE
  if (isAfter(attendanceDate, today)) {
    return AttendanceStatus.FUTURE;
  }

  // If no attendance record for a past date, mark as ABSENT
  if (!attendance.actualClockInTime && isBefore(attendanceDate, today)) {
    return AttendanceStatus.ABSENT;
  }

  // If there's no shift information, we can't determine if late
  if (!attendance.shift) {
    return attendance.actualClockInTime
      ? AttendanceStatus.ONTIME
      : AttendanceStatus.ABSENT;
  }

  const scheduledStart = new Date(attendance.shift.startDateTime);
  const actualClockIn = new Date(attendance.actualClockInTime);

  // Consider late if clocked in more than 15 minutes after scheduled start
  const lateThresholdMinutes = 15;
  const minutesLate = differenceInMinutes(actualClockIn, scheduledStart);

  if (minutesLate > lateThresholdMinutes) {
    return AttendanceStatus.LATE;
  }

  return AttendanceStatus.ONTIME;
}

/**
 * Transforms GraphQL attendance data into the format expected by AttendanceOverview component
 */
export function transformAttendanceData(
  attendanceRecords: GraphQLAttendance[],
  selectedMonth: Date
): {
  dailyAttendance: DailyAttendance[];
  summary: AttendanceSummary;
} {
  const monthStart = startOfMonth(selectedMonth);
  const monthEnd = endOfMonth(selectedMonth);

  // Get all days in the selected month
  const daysInMonth = eachDayOfInterval({
    start: monthStart,
    end: monthEnd,
  });

  // Create a map of attendance records by date
  const attendanceByDate = new Map<string, GraphQLAttendance>();
  attendanceRecords.forEach((record) => {
    const dateKey = format(new Date(record.date), "yyyy-MM-dd");
    attendanceByDate.set(dateKey, record);
  });

  // Initialize counters for summary
  let ontimeCount = 0;
  let lateCount = 0;
  let absentCount = 0;

  // Process each day and create attendance records
  const dailyAttendance: DailyAttendance[] = [];
  const recordsPerRow = 10; // Number of days to show per row

  // Group days into rows
  for (let i = 0; i < daysInMonth.length; i += recordsPerRow) {
    const rowDays = daysInMonth.slice(i, i + recordsPerRow);
    const records: AttendanceStatus[] = [];

    rowDays.forEach((day) => {
      const dateKey = format(day, "yyyy-MM-dd");
      const attendanceRecord = attendanceByDate.get(dateKey);

      let status: AttendanceStatus;

      if (attendanceRecord) {
        status = determineAttendanceStatus(attendanceRecord, day);
      } else {
        // No attendance record for this day
        const today = new Date();
        if (isAfter(day, today)) {
          status = AttendanceStatus.FUTURE;
        } else {
          status = AttendanceStatus.ABSENT;
        }
      }

      // Update counters (only count past days, not future days)
      const today = new Date();
      if (!isAfter(day, today)) {
        switch (status) {
          case AttendanceStatus.ONTIME:
            ontimeCount++;
            break;
          case AttendanceStatus.LATE:
            lateCount++;
            break;
          case AttendanceStatus.ABSENT:
            absentCount++;
            break;
        }
      }

      records.push(status);
    });

    dailyAttendance.push({
      day: "", // We don't use day labels in the current design
      records,
    });
  }

  return {
    dailyAttendance,
    summary: {
      ontime: ontimeCount,
      late: lateCount,
      absent: absentCount,
    },
  };
}

/**
 * Formats the month and year for display
 */
export function formatMonthYear(date: Date): string {
  return format(date, "MMMM yyyy");
}
