import { env } from "./constants/env";
import axios from "axios";
import * as SecureStore from "expo-secure-store";

export const getSession = () => SecureStore.getItemAsync("session");

export const fetchData = <TData, TVariables>(
  query: string,
  variables?: TVariables,
  options?: Record<string, string>
): (() => Promise<TData>) => {
  return async () => {
    const headers = {
      "Content-Type": "application/json",
      ...options,
    } as Record<string, string>;

    const session = await getSession();
    let token: string | undefined;

    if (session) {
      token = JSON.parse(session).access_token;
    }

    if (token && !("Authorization" in headers))
      headers["Authorization"] = `Bearer ${token}`;

    const res = await axios.post(
      env.GRAPHQL_ENDPOINT!,
      {
        query,
        variables,
      },
      { headers }
    );

    const { data } = res;

    if (data.errors) {
      const { message } = data.errors[0] || {};
      throw new Error(message || "Error…");
    }

    return data.data;
  };
};

export const axiosClient = axios.create({ baseURL: env.EXPO_PUBLIC_ENDPOINT });
