import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";

// Mock data for appointments
const appointments = [
  {
    id: "1",
    time: "12:00PM - 12:45PM",
    name: "<PERSON>",
    type: "Acupuncture",
    staff: "With Staff Member #1",
    avatar: "RC",
  },
];

// Days of the week
const daysOfWeek = ["Sun", "Mon", "<PERSON>e", "Wed", "Thu", "Fri", "Sat"];

export default function CalendarScreen() {
  const [selectedDateIndex, setSelectedDateIndex] = useState(0); // Index of the selected date in the week
  const [currentWeekOffset, setCurrentWeekOffset] = useState(0); // 0 = current week, -1 = last week, 1 = next week
  const [weekDates, setWeekDates] = useState<Date[]>([]);

  // Generate dates for the current week
  useEffect(() => {
    const today = new Date();
    const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Calculate the start of the week (Sunday)
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - currentDay + 7 * currentWeekOffset);

    // Generate an array of dates for the week
    const dates: Date[] = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      dates.push(date);
    }

    setWeekDates(dates);

    // If we're on the current week (offset = 0), select today
    if (currentWeekOffset === 0) {
      setSelectedDateIndex(currentDay);
    } else {
      setSelectedDateIndex(0);
    }
  }, [currentWeekOffset]);

  // Navigate to previous week
  const goToPreviousWeek = () => {
    setCurrentWeekOffset(currentWeekOffset - 1);
  };

  // Navigate to next week
  const goToNextWeek = () => {
    setCurrentWeekOffset(currentWeekOffset + 1);
  };

  // Format date as a number (1-31)
  const formatDateNumber = (date: Date) => {
    return date.getDate();
  };

  // Check if a date has an event (for the dot indicator)
  const hasEvent = (date: Date) => {
    // For demo purposes, let's say Monday and Tuesday have events
    const day = date.getDay();
    return day === 1 || day === 2;
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Month Display */}
      {weekDates[selectedDateIndex] && (
        <View style={styles.monthHeader}>
          <Text style={styles.monthText}>
            {weekDates[selectedDateIndex].toLocaleString("default", {
              month: "long",
              year: "numeric",
            })}
          </Text>
        </View>
      )}

      {/* Calendar Week View */}
      <View style={styles.calendarContainer}>
        <View style={styles.weekContainer}>
          <TouchableOpacity onPress={goToPreviousWeek} style={styles.navButton}>
            <Ionicons name="chevron-back" size={18} color={Colors.primary} />
          </TouchableOpacity>

          {daysOfWeek.map((day, index) => (
            <Text key={index} style={styles.weekDayText}>
              {day}
            </Text>
          ))}

          <TouchableOpacity onPress={goToNextWeek} style={styles.navButton}>
            <Ionicons name="chevron-forward" size={18} color={Colors.primary} />
          </TouchableOpacity>
        </View>

        <View style={styles.datesContainer}>
          <View style={styles.navButtonPlaceholder} />

          <View style={styles.datesRow}>
            {weekDates.map((date, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.dateCircle,
                  selectedDateIndex === index
                    ? styles.selectedDateCircle
                    : null,
                ]}
                onPress={() => {
                  setSelectedDateIndex(index);
                }}
              >
                <Text
                  style={[
                    styles.dateText,
                    selectedDateIndex === index
                      ? styles.selectedDateText
                      : null,
                  ]}
                >
                  {formatDateNumber(date)}
                </Text>
                {hasEvent(date) && <View style={styles.dotIndicator} />}
              </TouchableOpacity>
            ))}
          </View>

          <View style={styles.navButtonPlaceholder} />
        </View>
      </View>

      {/* Day View */}
      <ScrollView style={styles.dayViewContainer}>
        {/* Extra space at the top for scrolling */}
        <View style={styles.scrollPadding} />

        {/* Current Week Dates */}
        {weekDates.map((date, index) => {
          const isSelected = index === selectedDateIndex;

          return (
            <TouchableOpacity
              key={`day-${index}`}
              style={[
                styles.dayViewPlaceholder,
                isSelected && styles.selectedDayHighlight,
              ]}
              onPress={() => setSelectedDateIndex(index)}
              activeOpacity={0.7}
            >
              <View style={styles.dayInfoContainer}>
                <Text
                  style={[
                    styles.currentDayNumber,
                    isSelected && styles.selectedDayText,
                  ]}
                >
                  {date.getDate()}
                </Text>
                <Text
                  style={[
                    styles.currentDayName,
                    isSelected && styles.selectedDayText,
                  ]}
                >
                  {daysOfWeek[date.getDay()]}
                </Text>
              </View>

              {/* Show appointment for selected date */}
              {isSelected &&
                (selectedDateIndex === 3 && currentWeekOffset === 0 ? (
                  <View style={styles.appointmentCard}>
                    <View style={styles.appointmentContent}>
                      <Text style={styles.appointmentTime}>
                        {appointments[0].time}
                      </Text>
                      <Text style={styles.appointmentName}>
                        {appointments[0].name}
                      </Text>
                    </View>
                  </View>
                ) : (
                  <View style={styles.emptyDayContainer}>
                    <Text style={styles.emptyDayText}>No appointments</Text>
                  </View>
                ))}
            </TouchableOpacity>
          );
        })}

        {/* Extra space at the bottom for scrolling */}
        <View style={styles.bottomScrollPadding} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  monthHeader: {
    backgroundColor: Colors.white,
    paddingVertical: 10,
    paddingLeft: 20,
    paddingRight: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  monthText: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.text,
    textAlign: "left",
  },
  calendarContainer: {
    backgroundColor: Colors.white,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  weekContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 8,
    marginBottom: 4,
  },
  navButton: {
    padding: 4,
    width: 26,
    alignItems: "center",
  },
  weekDayText: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: "center",
    width: 40,
  },
  datesContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 8,
  },
  navButtonPlaceholder: {
    width: 26,
  },
  datesRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    flex: 1,
  },
  dateCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  selectedDateCircle: {
    backgroundColor: Colors.primary,
  },
  dateText: {
    fontSize: 16,
    color: Colors.text,
    fontWeight: "500",
  },
  selectedDateText: {
    color: Colors.white,
    fontWeight: "bold",
  },
  dotIndicator: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: Colors.primary,
    position: "absolute",
    bottom: 4,
  },
  dayViewContainer: {
    flex: 1,
    backgroundColor: "#F0F0F0", // Light grey background
    paddingBottom: 20,
  },

  selectedDayContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    paddingHorizontal: 20,
    backgroundColor: "#F0F0F0", // Light grey background
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  selectedDayHighlight: {
    backgroundColor: "#E8F5E9", // Light green background for selected date
    borderLeftWidth: 3,
    borderLeftColor: Colors.primary,
  },
  dayInfoContainer: {
    width: 40,
    marginRight: 16,
    alignItems: "center",
  },
  currentDayNumber: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.text,
  },
  currentDayName: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  selectedDayText: {
    color: Colors.primary,
    fontWeight: "bold",
  },
  appointmentCard: {
    flex: 1,
    backgroundColor: Colors.white,
    borderRadius: 8,
    padding: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  appointmentContent: {
    flex: 1,
  },
  appointmentTime: {
    fontSize: 12,
    fontWeight: "bold",
    color: Colors.primary,
    marginBottom: 2,
  },
  appointmentName: {
    fontSize: 14,
    fontWeight: "bold",
    color: Colors.text,
  },
  emptyDayContainer: {
    flex: 1,
    backgroundColor: Colors.white,
    borderRadius: 8,
    padding: 10,
    alignItems: "center",
    justifyContent: "center",
  },
  emptyDayText: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  dayViewPlaceholder: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: "#F0F0F0", // Light grey background
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    height: 100, // Decreased height as requested
  },
  scrollPadding: {
    height: 20,
  },
  bottomScrollPadding: {
    height: 100, // Extra padding at the bottom
  },
});
