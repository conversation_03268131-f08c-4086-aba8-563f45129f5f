import { Colors } from "@/constants/Colors";
import { AnnouncementsQuery } from "@/generated/graphql";
import { Ionicons } from "@expo/vector-icons";
import FontAwesome from "@expo/vector-icons/FontAwesome";
import { format } from "date-fns";
import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withSequence,
  withTiming,
} from "react-native-reanimated";

export interface AnnouncementItemProps {
  announcement: AnnouncementsQuery["anouncements"][number];
  onPress: (announcement: AnnouncementsQuery["anouncements"][number]) => void;
}

/**
 * Announcement item component for the announcement list with animations
 */
export function AnnouncementItem({
  onPress,
  announcement,
}: AnnouncementItemProps) {
  const { date, description, id, title } = announcement;

  // Animation values
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);

  // Handle press animation
  const handlePress = () => {
    // Scale animation
    scale.value = withSequence(
      withTiming(0.98, { duration: 100, easing: Easing.inOut(Easing.quad) }),
      withTiming(1, { duration: 200, easing: Easing.out(Easing.quad) })
    );

    // Subtle horizontal movement
    translateX.value = withSequence(
      withTiming(5, { duration: 100 }),
      withTiming(0, { duration: 200, easing: Easing.out(Easing.quad) })
    );

    // Call the onPress callback
    onPress(announcement);
  };

  // Animated style
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }, { translateX: translateX.value }],
  }));

  return (
    <Animated.View style={animatedStyle} key={id}>
      <TouchableOpacity
        style={styles.container}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <Ionicons
          name="megaphone"
          size={22}
          color={Colors.primaryDark}
          style={{
            marginRight: 16,
            backgroundColor: Colors.lightGray,
            padding: 8,
            borderRadius: 30,
            elevation: 1,
          }}
        />
        <View style={styles.contentContainer}>
          <View style={styles.header}>
            <Text style={styles.senderName}>{title}</Text>
          </View>

          <Text style={styles.message} numberOfLines={1}>
            {description}
          </Text>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  contentContainer: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 4,
  },
  senderName: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.text,
  },
  time: {
    fontSize: 12,
    color: Colors.textLight,
  },
  message: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  unreadIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.notification,
    marginLeft: 8,
  },
});
