import React, { useRef, useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Pressable,
  Image,
  SafeAreaView,
} from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { Colors } from "@/constants/Colors";
import { Ionicons } from "@expo/vector-icons";
import { CameraView, useCameraPermissions } from "expo-camera";
import { useSession } from "@/providers/auth-provider";
import { useClockInMutation } from "@/generated/graphql";
import { useQueryClient } from "@tanstack/react-query";

export default function ClockInScanScreen() {
  const router = useRouter();
  const { session } = useSession();
  const queryClient = useQueryClient();
  const ref = useRef<CameraView>(null);
  const { shiftId, locationId } = useLocalSearchParams<{
    shiftId: string;
    locationId: string;
  }>();

  const [showCamera, setShowCamera] = useState(false);
  const [isClockingin, setIsClockingin] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [permission, requestPermission] = useCameraPermissions();

  const { mutateAsync: clockIn, isLoading: isClockingIn } = useClockInMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["GetUserAttendance"] });
      Alert.alert("Success", "You have successfully clocked in!", [
        {
          text: "OK",
          onPress: () => router.back(),
        },
      ]);
    },
    onError: (error) => {
      if (error instanceof Error) {
        Alert.alert("Error", error.message);
      }
      setIsClockingin(false);
    },
  });

  const takePicture = async () => {
    try {
      setIsClockingin(true);
      const photo = await ref.current?.takePictureAsync({
        base64: true,
        quality: 0.5, // Optimal balance between file size and quality
      });

      if (photo?.base64) {
        const base64Img = `data:image/jpeg;base64,${photo.base64}`;
        setCapturedImage(base64Img);
        setShowCamera(false);
      }
    } catch (err) {
      console.error("Failed to take picture", err);
      Alert.alert("Error", "Failed to capture image. Please try again.");
    } finally {
      setIsClockingin(false);
    }
  };

  const handleConfirmClockIn = async () => {
    if (!session?.userId || !capturedImage || !shiftId || !locationId) {
      Alert.alert("Error", "Missing required information. Please try again.");
      return;
    }

    try {
      setIsClockingin(true);

      await clockIn({
        clockInInput: {
          base64Img: capturedImage,
          date: new Date(),
          locationId,
          shiftId,
        },
      });
    } catch (error) {
      if (error instanceof Error) {
        Alert.alert("Error", error.message);
      }
      setIsClockingin(false);
    }
  };

  const handleRetake = () => {
    setCapturedImage(null);
    setShowCamera(true);
  };

  const handleStartScanning = async () => {
    if (!permission) {
      return;
    }

    if (!permission.granted) {
      const { granted } = await requestPermission();
      if (!granted) {
        Alert.alert(
          "Permission Required",
          "Camera permission is required to clock in."
        );
        return;
      }
    }

    setShowCamera(true);
  };

  const handleBackPress = () => {
    if (showCamera) {
      setShowCamera(false);
    } else if (capturedImage) {
      setCapturedImage(null);
    } else {
      router.back();
    }
  };

  if (!permission) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  // Camera view
  if (showCamera) {
    return (
      <View style={styles.cameraContainer}>
        <CameraView
          style={styles.camera}
          ref={ref}
          facing="front"
          mute={false}
          responsiveOrientationWhenOrientationLocked
        >
          {/* Back button */}
          <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
            <Ionicons name="close" size={32} color="white" />
          </TouchableOpacity>

          {/* Face guide overlay */}
          <View style={styles.faceGuideContainer}>
            <View style={styles.faceGuide}>
              <View style={styles.faceGuideCorner} />
              <View style={[styles.faceGuideCorner, styles.topRight]} />
              <View style={[styles.faceGuideCorner, styles.bottomLeft]} />
              <View style={[styles.faceGuideCorner, styles.bottomRight]} />
            </View>
            <Text style={styles.guideText}>
              Position your face within the frame
            </Text>
          </View>

          {/* Capture button */}
          <View style={styles.shutterContainer}>
            <Pressable
              onPress={takePicture}
              disabled={isClockingin || isClockingIn}
            >
              {({ pressed }) => (
                <View
                  style={[
                    styles.shutterBtn,
                    {
                      opacity:
                        pressed || isClockingin || isClockingIn ? 0.5 : 1,
                    },
                  ]}
                >
                  {isClockingin || isClockingIn ? (
                    <ActivityIndicator size="small" color="white" />
                  ) : (
                    <View style={styles.shutterBtnInner} />
                  )}
                </View>
              )}
            </Pressable>
          </View>
        </CameraView>
      </View>
    );
  }

  // Image preview
  if (capturedImage) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            onPress={handleBackPress}
            style={styles.headerButton}
          >
            <Ionicons name="arrow-back" size={24} color={Colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Confirm Clock In</Text>
          <View style={styles.headerButton} />
        </View>

        <View style={styles.previewContainer}>
          <Text style={styles.previewTitle}>Review Your Photo</Text>
          <Text style={styles.previewSubtitle}>
            Make sure your face is clearly visible
          </Text>

          <View style={styles.imageContainer}>
            <Image
              source={{ uri: capturedImage }}
              style={styles.previewImage}
            />
          </View>

          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionButton, styles.retakeButton]}
              onPress={handleRetake}
              disabled={isClockingin}
            >
              <Ionicons name="camera" size={20} color={Colors.primary} />
              <Text style={styles.retakeButtonText}>Retake</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.confirmButton]}
              onPress={handleConfirmClockIn}
              disabled={isClockingin}
            >
              {isClockingin ? (
                <ActivityIndicator size="small" color={Colors.white} />
              ) : (
                <Ionicons name="checkmark" size={20} color={Colors.white} />
              )}
              <Text style={styles.confirmButtonText}>
                {isClockingin ? "Clocking In..." : "Confirm"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  // Instructions screen
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBackPress} style={styles.headerButton}>
          <Ionicons name="arrow-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Clock In</Text>
        <View style={styles.headerButton} />
      </View>

      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Ionicons name="camera" size={64} color={Colors.primary} />
        </View>

        <Text style={styles.title}>Scan Your Face to Clock In</Text>
        <Text style={styles.subtitle}>
          We'll use face recognition to verify your identity and record your
          attendance.
        </Text>

        {/* Instructions */}
        <View style={styles.instructionsContainer}>
          <Text style={styles.instructionsTitle}>For best results:</Text>
          <Text style={styles.instructionText}>• Ensure good lighting</Text>
          <Text style={styles.instructionText}>
            • Look directly at the camera
          </Text>
          <Text style={styles.instructionText}>
            • Keep your face within the frame
          </Text>
          <Text style={styles.instructionText}>
            • Remove glasses if possible
          </Text>
        </View>

        {/* Start button */}
        <TouchableOpacity
          style={styles.startButton}
          onPress={handleStartScanning}
          activeOpacity={0.8}
        >
          <Ionicons name="camera" size={20} color={Colors.white} />
          <Text style={styles.startButtonText}>Start Face Scan</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerButton: {
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.text,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingVertical: 32,
    alignItems: "center",
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: Colors.primaryLight + "20",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: "700",
    color: Colors.text,
    textAlign: "center",
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 32,
  },
  instructionsContainer: {
    width: "100%",
    backgroundColor: Colors.white,
    borderRadius: 16,
    padding: 20,
    marginBottom: 32,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
    marginBottom: 16,
  },
  instructionText: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 6,
    lineHeight: 20,
  },
  startButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.primary,
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
    width: "100%",
  },
  startButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.white,
    marginLeft: 8,
  },
  // Camera styles
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  backButton: {
    position: "absolute",
    top: 50,
    left: 20,
    zIndex: 99,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    borderRadius: 20,
    padding: 8,
  },
  faceGuideContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  faceGuide: {
    width: 200,
    height: 250,
    position: "relative",
    marginBottom: 20,
  },
  faceGuideCorner: {
    position: "absolute",
    width: 30,
    height: 30,
    borderColor: Colors.white,
    borderWidth: 3,
    borderTopWidth: 3,
    borderLeftWidth: 3,
    borderRightWidth: 0,
    borderBottomWidth: 0,
    top: 0,
    left: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    left: "auto",
    borderTopWidth: 3,
    borderRightWidth: 3,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    top: "auto",
    borderBottomWidth: 3,
    borderLeftWidth: 3,
    borderTopWidth: 0,
    borderRightWidth: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    top: "auto",
    left: "auto",
    borderBottomWidth: 3,
    borderRightWidth: 3,
    borderTopWidth: 0,
    borderLeftWidth: 0,
  },
  guideText: {
    color: Colors.white,
    fontSize: 16,
    textAlign: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  shutterContainer: {
    position: "absolute",
    bottom: 50,
    alignSelf: "center",
  },
  shutterBtn: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 4,
    borderColor: Colors.white,
  },
  shutterBtnInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.white,
  },
  // Preview styles
  previewContainer: {
    flex: 1,
    paddingHorizontal: 24,
    paddingVertical: 32,
    alignItems: "center",
  },
  previewTitle: {
    fontSize: 24,
    fontWeight: "700",
    color: Colors.text,
    textAlign: "center",
    marginBottom: 8,
  },
  previewSubtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: "center",
    marginBottom: 32,
  },
  imageContainer: {
    width: 250,
    height: 300,
    borderRadius: 20,
    overflow: "hidden",
    marginBottom: 32,
    borderWidth: 3,
    borderColor: Colors.primary,
  },
  previewImage: {
    width: "100%",
    height: "100%",
  },
  actionButtons: {
    flexDirection: "row",
    gap: 16,
    width: "100%",
  },
  actionButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    borderRadius: 12,
  },
  retakeButton: {
    backgroundColor: Colors.white,
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  confirmButton: {
    backgroundColor: Colors.primary,
  },
  retakeButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.primary,
    marginLeft: 8,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.white,
    marginLeft: 8,
  },
});
