import React, { useState, useRef } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Image,
  Animated,
  Easing,
} from "react-native";
import { Colors } from "@/constants/Colors";
import { Ionicons } from "@expo/vector-icons";
import { StatusBar } from "expo-status-bar";
import { useRouter, useLocalSearchParams } from "expo-router";

// Sample data for document files
const documentFiles = {
  "1": [
    {
      id: "101",
      name: "Jakarta Report Q1.pdf",
      size: "2.4 MB",
      date: "2023-03-15",
    },
    {
      id: "102",
      name: "Jakarta Office Lease.pdf",
      size: "1.8 MB",
      date: "2023-02-10",
    },
    {
      id: "103",
      name: "Jakarta Staff List.xlsx",
      size: "0.9 MB",
      date: "2023-04-05",
    },
    {
      id: "104",
      name: "Jakarta Budget 2023.xlsx",
      size: "1.2 MB",
      date: "2023-01-20",
    },
    {
      id: "105",
      name: "Jakarta Office Photos.zip",
      size: "15.7 MB",
      date: "2023-05-12",
    },
  ],
  "2": [
    {
      id: "201",
      name: "Bekasi Project Plan.pdf",
      size: "3.1 MB",
      date: "2023-04-18",
    },
    {
      id: "202",
      name: "Bekasi Client Meeting.docx",
      size: "0.7 MB",
      date: "2023-05-02",
    },
    {
      id: "203",
      name: "Bekasi Site Photos.zip",
      size: "22.5 MB",
      date: "2023-05-10",
    },
  ],
  "3": [
    {
      id: "301",
      name: "Surakarta Annual Report.pdf",
      size: "4.2 MB",
      date: "2023-01-15",
    },
    {
      id: "302",
      name: "Surakarta Contracts.zip",
      size: "8.7 MB",
      date: "2023-03-22",
    },
  ],
  "4": [
    {
      id: "401",
      name: "Yogjakarta Event Photos.zip",
      size: "35.2 MB",
      date: "2023-04-30",
    },
    {
      id: "402",
      name: "Yogjakarta Venue Contract.pdf",
      size: "1.5 MB",
      date: "2023-03-15",
    },
  ],
  "5": [
    {
      id: "501",
      name: "Malang Project Proposal.pdf",
      size: "2.8 MB",
      date: "2023-02-28",
    },
    {
      id: "502",
      name: "Malang Client List.xlsx",
      size: "0.6 MB",
      date: "2023-04-10",
    },
  ],
  "6": [
    {
      id: "601",
      name: "Holiday Schedule 2023.pdf",
      size: "0.5 MB",
      date: "2023-01-05",
    },
    {
      id: "602",
      name: "Team Building Photos.zip",
      size: "45.3 MB",
      date: "2023-04-22",
    },
  ],
  "7": [
    {
      id: "701",
      name: "Monthly Report - January.pdf",
      size: "1.8 MB",
      date: "2023-02-05",
    },
    {
      id: "702",
      name: "Monthly Report - February.pdf",
      size: "2.1 MB",
      date: "2023-03-07",
    },
  ],
  "8": [
    {
      id: "801",
      name: "ISO Certification.pdf",
      size: "3.2 MB",
      date: "2023-01-20",
    },
    {
      id: "802",
      name: "Staff Certifications.zip",
      size: "12.5 MB",
      date: "2023-03-15",
    },
  ],
};

// Folder names mapping
const folderNames = {
  "1": "Jakarta",
  "2": "Bekasi",
  "3": "Surakarta",
  "4": "Yogjakarta",
  "5": "Malang",
  "6": "Holidays",
  "7": "Reports",
  "8": "Certificates",
};

// Get file icon and color based on file extension
const getFileIconInfo = (fileName: string) => {
  const extension = fileName.split(".").pop()?.toLowerCase();

  switch (extension) {
    case "pdf":
      return { icon: "document-text", color: "#E94335" };
    case "docx":
    case "doc":
      return { icon: "document-text", color: "#4285F4" };
    case "xlsx":
    case "xls":
      return { icon: "grid", color: "#0F9D58" };
    case "zip":
      return { icon: "archive", color: "#F4B400" };
    case "jpg":
    case "jpeg":
    case "png":
      return { icon: "image", color: "#9C27B0" };
    default:
      return { icon: "document", color: "#757575" };
  }
};

interface DocumentItemProps {
  name: string;
  size: string;
  date: string;
  onPress: () => void;
}

/**
 * Document item component with animations
 */
function DocumentItem({
  name,
  size,
  date,
  onPress,
  index = 0,
}: DocumentItemProps & { index?: number }) {
  const { icon, color } = getFileIconInfo(name);
  const slideAnim = useRef(new Animated.Value(-50)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  // Start entrance animation
  React.useEffect(() => {
    const delay = index * 50; // Stagger the animation

    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        delay,
        useNativeDriver: true,
        easing: Easing.out(Easing.ease),
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 300,
        delay,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  return (
    <Animated.View
      style={{
        opacity: opacityAnim,
        transform: [{ translateX: slideAnim }],
      }}
    >
      <TouchableOpacity
        style={styles.documentItem}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <View
          style={[styles.fileIconContainer, { backgroundColor: `${color}20` }]}
        >
          <Ionicons name={icon as any} size={24} color={color} />
        </View>

        <View style={styles.fileInfo}>
          <Text style={styles.fileName} numberOfLines={1}>
            {name}
          </Text>
          <View style={styles.fileDetails}>
            <Text style={styles.fileSize}>{size}</Text>
            <Text style={styles.fileDot}>•</Text>
            <Text style={styles.fileDate}>{date}</Text>
          </View>
        </View>

        <TouchableOpacity style={styles.moreButton}>
          <Ionicons
            name="ellipsis-vertical"
            size={20}
            color={Colors.textSecondary}
          />
        </TouchableOpacity>
      </TouchableOpacity>
    </Animated.View>
  );
}

export default function DocumentDetailsScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const folderId = typeof id === "string" ? id : "1";
  const folderName =
    folderNames[folderId as keyof typeof folderNames] || "Documents";
  const files = documentFiles[folderId as keyof typeof documentFiles] || [];

  const [searchQuery, setSearchQuery] = useState("");
  const fabAnim = useRef(new Animated.Value(0)).current;

  // Animate FAB entrance
  React.useEffect(() => {
    Animated.spring(fabAnim, {
      toValue: 1,
      tension: 50,
      friction: 7,
      delay: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  const handleBackPress = () => {
    router.back();
  };

  const handleDocumentPress = (document: any) => {
    // Handle document opening logic here
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBackPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons name="chevron-back" size={24} color={Colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{folderName}</Text>
        <TouchableOpacity style={styles.searchButton}>
          <Ionicons name="search-outline" size={24} color={Colors.white} />
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {files.length > 0 ? (
          <FlatList
            data={files}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            renderItem={({ item, index }) => (
              <DocumentItem
                name={item.name}
                size={item.size}
                date={item.date}
                onPress={() => handleDocumentPress(item)}
                index={index}
              />
            )}
          />
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons
              name="document-outline"
              size={64}
              color={Colors.textLight}
            />
            <Text style={styles.emptyText}>No documents found</Text>
          </View>
        )}
      </View>

      {/* Animated FAB */}
      <Animated.View
        style={[
          styles.fab,
          {
            transform: [
              { scale: fabAnim },
              {
                rotate: fabAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: ["135deg", "0deg"],
                }),
              },
            ],
          },
        ]}
      >
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={() => {}}
          style={styles.fabButton}
        >
          <Ionicons name="add" size={24} color={Colors.white} />
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.white,
    flex: 1,
    textAlign: "center",
  },
  searchButton: {
    padding: 4,
  },
  content: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  documentItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.05)",
  },
  fileIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    marginRight: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.text,
    marginBottom: 4,
  },
  fileDetails: {
    flexDirection: "row",
    alignItems: "center",
  },
  fileSize: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  fileDot: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginHorizontal: 4,
  },
  fileDate: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  moreButton: {
    padding: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginTop: 16,
  },
  fab: {
    position: "absolute",
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.primary,
    justifyContent: "center",
    alignItems: "center",
  },
  fabButton: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
});
