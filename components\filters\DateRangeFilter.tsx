import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Modal,
  StyleProp,
  ViewStyle,
} from "react-native";
import { Calendar } from "react-native-calendars";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";
import { Button } from "../ui/Button";

export interface DateRangeFilterProps {
  fromDate: string | null;
  toDate: string | null;
  onDateRangeChange: (fromDate: string | null, toDate: string | null) => void;
  containerStyle?: StyleProp<ViewStyle>;
}

export function DateRangeFilter({
  fromDate,
  toDate,
  onDateRangeChange,
  containerStyle,
}: DateRangeFilterProps) {
  const [modalVisible, setModalVisible] = useState(false);
  const [tempFromDate, setTempFromDate] = useState<string | null>(fromDate);
  const [tempToDate, setTempToDate] = useState<string | null>(toDate);

  // Format date for display
  const formatDate = (dateString: string | null): string => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  // Get display text for date range
  const getDisplayText = (): string => {
    if (!fromDate && !toDate) return "Select date range";
    if (fromDate && toDate) {
      return `${formatDate(fromDate)} - ${formatDate(toDate)}`;
    }
    if (fromDate) return `From ${formatDate(fromDate)}`;
    if (toDate) return `To ${formatDate(toDate)}`;
    return "Select date range";
  };

  // Handle day press in calendar
  const handleDayPress = (day: { dateString: string }) => {
    const selectedDate = day.dateString;

    if (!tempFromDate || (tempFromDate && tempToDate)) {
      // Start new selection
      setTempFromDate(selectedDate);
      setTempToDate(null);
    } else {
      // Complete the selection
      const start = new Date(tempFromDate);
      const end = new Date(selectedDate);

      if (end < start) {
        // If end date is before start date, swap them
        setTempFromDate(selectedDate);
        setTempToDate(tempFromDate);
      } else {
        setTempToDate(selectedDate);
      }
    }
  };

  // Confirm date range selection
  const confirmDateRange = () => {
    onDateRangeChange(tempFromDate, tempToDate);
    setModalVisible(false);
  };

  // Cancel date range selection
  const cancelDateRange = () => {
    setTempFromDate(fromDate);
    setTempToDate(toDate);
    setModalVisible(false);
  };

  // Clear date range
  const clearDateRange = () => {
    setTempFromDate(null);
    setTempToDate(null);
    onDateRangeChange(null, null);
    setModalVisible(false);
  };

  // Get marked dates for calendar
  const getMarkedDates = () => {
    const markedDates: any = {};

    if (tempFromDate && tempToDate) {
      const start = new Date(tempFromDate);
      const end = new Date(tempToDate);
      const current = new Date(start);

      while (current <= end) {
        const dateString = current.toISOString().split("T")[0];

        if (dateString === tempFromDate && dateString === tempToDate) {
          // Single day selection
          markedDates[dateString] = {
            startingDay: true,
            endingDay: true,
            color: Colors.primary,
            textColor: Colors.text,
          };
        } else if (dateString === tempFromDate) {
          // Start date
          markedDates[dateString] = {
            startingDay: true,
            color: Colors.primary,
            textColor: Colors.text,
          };
        } else if (dateString === tempToDate) {
          // End date
          markedDates[dateString] = {
            endingDay: true,
            color: Colors.primary,
            textColor: Colors.text,
          };
        } else {
          // Middle dates
          markedDates[dateString] = {
            color: Colors.primaryLight,
            textColor: Colors.text,
          };
        }

        current.setDate(current.getDate() + 1);
      }
    } else if (tempFromDate) {
      // Only start date selected
      markedDates[tempFromDate] = {
        selected: true,
        selectedColor: Colors.primary,
        selectedTextColor: Colors.text,
      };
    }

    return markedDates;
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <TouchableOpacity
        style={styles.dateSelector}
        onPress={() => setModalVisible(true)}
      >
        <View style={styles.dateSelectorContent}>
          <Ionicons name="calendar-outline" size={16} color={Colors.primary} />
          <Text style={styles.dateText}>{getDisplayText()}</Text>
        </View>
        <Ionicons name="chevron-down" size={16} color={Colors.textSecondary} />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={cancelDateRange}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={cancelDateRange}>
              <Text style={styles.cancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Select Date Range</Text>
            <TouchableOpacity onPress={clearDateRange}>
              <Text style={styles.clearText}>Clear</Text>
            </TouchableOpacity>
          </View>

          <Calendar
            onDayPress={handleDayPress}
            markedDates={getMarkedDates()}
            markingType="period"
            theme={{
              calendarBackground: Colors.white,
              textSectionTitleColor: Colors.textSecondary,
              selectedDayBackgroundColor: Colors.primary,
              selectedDayTextColor: Colors.text,
              todayTextColor: Colors.primary,
              dayTextColor: Colors.text,
              textDisabledColor: Colors.textLight,
              dotColor: Colors.primary,
              selectedDotColor: Colors.white,
              arrowColor: Colors.primary,
              monthTextColor: Colors.text,
              indicatorColor: Colors.primary,
            }}
          />

          <View style={styles.calendarFooter}>
            <Button
              title={
                tempFromDate && !tempToDate
                  ? "Select End Date"
                  : "Confirm Selection"
              }
              onPress={confirmDateRange}
              disabled={!tempFromDate}
              icon="checkmark-outline"
            />
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 8,
  },
  dateSelector: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: Colors.white,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  dateSelectorContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  dateText: {
    fontSize: 14,
    color: Colors.text,
    marginLeft: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.text,
  },
  cancelText: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  clearText: {
    fontSize: 16,
    color: Colors.error,
  },
  calendarFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
});
