import React from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";

export interface ClockStatus {
  isClockedIn: boolean;
  clockInTime?: Date | null;
  attendanceId?: string | null;
}

interface ClockInOutButtonProps {
  clockStatus: ClockStatus;
  onClockIn: () => void;
  onClockOut: () => void;
  loading?: boolean;
  disabled?: boolean;
}

export function ClockInOutButton({
  clockStatus,
  onClockIn,
  onClockOut,
  loading = false,
  disabled = false,
}: ClockInOutButtonProps) {
  const { isClockedIn } = clockStatus;

  const formatTime = (date: Date | null): string => {
    if (!date) return "";
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  return (
    <View style={styles.container}>
      <View style={styles.buttonContainer}>
        {/* Clock In Button */}
        <TouchableOpacity
          style={[
            styles.button,
            styles.clockInButton,
            (isClockedIn || disabled) && styles.disabledButton,
          ]}
          onPress={onClockIn}
          disabled={isClockedIn || disabled || loading}
          activeOpacity={0.8}
        >
          <View style={styles.buttonContent}>
            <View style={styles.iconContainer}>
              {loading && !isClockedIn ? (
                <ActivityIndicator size="small" color={Colors.white} />
              ) : (
                <Ionicons
                  name="log-in-outline"
                  size={24}
                  color={
                    isClockedIn || disabled ? Colors.textLight : Colors.white
                  }
                />
              )}
            </View>
            <View style={styles.textContainer}>
              <Text
                style={[
                  styles.buttonText,
                  (isClockedIn || disabled) && styles.disabledButtonText,
                ]}
              >
                Clock In
              </Text>
              {isClockedIn && clockStatus.clockInTime && (
                <Text style={styles.timeText}>
                  {formatTime(clockStatus.clockInTime)}
                </Text>
              )}
            </View>
          </View>
        </TouchableOpacity>

        {/* Clock Out Button */}
        <TouchableOpacity
          style={[
            styles.button,
            styles.clockOutButton,
            (!isClockedIn || disabled) && styles.disabledButton,
          ]}
          onPress={onClockOut}
          disabled={!isClockedIn || disabled || loading}
          activeOpacity={0.8}
        >
          <View style={styles.buttonContent}>
            <View style={styles.iconContainer}>
              {loading && isClockedIn ? (
                <ActivityIndicator size="small" color={Colors.white} />
              ) : (
                <Ionicons
                  name="log-out-outline"
                  size={24}
                  color={
                    !isClockedIn || disabled ? Colors.textLight : Colors.white
                  }
                />
              )}
            </View>
            <View style={styles.textContainer}>
              <Text
                style={[
                  styles.buttonText,
                  (!isClockedIn || disabled) && styles.disabledButtonText,
                ]}
              >
                Clock Out
              </Text>
              {!isClockedIn && (
                <Text style={styles.timeText}>Not clocked in</Text>
              )}
            </View>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  buttonContainer: {
    flexDirection: "row",
    borderRadius: 16,
    overflow: "hidden",
    elevation: 0, // No shadows as per user preference
  },
  button: {
    flex: 1,
    paddingVertical: 20,
    paddingHorizontal: 16,
    minHeight: 80,
    justifyContent: "center",
  },
  clockInButton: {
    backgroundColor: Colors.primary,
    borderTopLeftRadius: 16,
    borderBottomLeftRadius: 16,
  },
  clockOutButton: {
    backgroundColor: Colors.error,
    borderTopRightRadius: 16,
    borderBottomRightRadius: 16,
  },
  disabledButton: {
    backgroundColor: Colors.lightGray,
    opacity: 0.6,
  },
  buttonContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "rgba(255,255,255,0.2)",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: "700",
    color: Colors.white,
  },
  disabledButtonText: {
    color: Colors.textSecondary,
  },
  timeText: {
    fontSize: 12,
    color: "rgba(23, 23, 23, 0.8)",
    marginTop: 2,
  },
});
