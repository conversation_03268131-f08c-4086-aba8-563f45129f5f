import { format } from "date-fns";
import { GetShiftsByUserQuery } from "../generated/graphql";

export interface Location {
  _id: string;
  name: string;
  address: string;
}

export interface User {
  _id: string;
  name: string;
  email: string;
}

export interface Shift {
  _id: string;
  location: Location | string;
  startDateTime: Date | string;
  endDateTime: Date | string;
  overTime?: Date | string;
  users: (User | string)[];
  isRecurring?: boolean;
  recurringId?: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// For the agenda calendar
export interface ShiftAgendaItem {
  name: string;
  day: string;
  location: string;
  startTime: string;
  endTime: string;
  shiftId: string;
  // Add full datetime objects for time comparison
  startDateTime: Date;
  endDateTime: Date;
}

/**
 * Maps a shift from the GraphQL query to a ShiftAgendaItem for display in the agenda
 * @param shift - Shift data from GetShiftsByUserQuery
 * @returns ShiftAgendaItem formatted for the agenda display
 */
export const shiftAgendaItemMapper = (
  shift: GetShiftsByUserQuery["getUserShifts"][number]
): ShiftAgendaItem => {
  const startDateTime = new Date(shift.startDateTime);
  const endDateTime = new Date(shift.endDateTime);

  return {
    day: format(startDateTime, "dd EEE MM"),
    name:
      shift.users?.map((user) => user.fullname).join(", ") ||
      "No assigned guards",
    startTime: format(startDateTime, "hh:mm a"),
    endTime: format(endDateTime, "hh:mm a"),
    location: shift.location?.name || "No location specified",
    shiftId: shift.id,
    // Include full datetime objects for time comparison
    startDateTime,
    endDateTime,
  };
};

export type SVGProps = {
  width: number;
  height: number;
  className?: string;
  color?: string;
};
