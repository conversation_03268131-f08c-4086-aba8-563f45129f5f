import { StatusBar } from "@/components/ui/StatusBar";
import { Colors } from "@/constants/Colors";
import { useGetShiftsByUserQuery } from "@/generated/graphql";
import { useSession } from "@/providers/auth-provider";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import React, { useMemo, useState } from "react";
import {
  ActivityIndicator,
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  withDelay,
} from "react-native-reanimated";
import { Calendar, DateData } from "react-native-calendars";
import { ShiftAgendaItem, shiftAgendaItemMapper } from "../../types";
import LoadingScreen from "@/components/LoadingView";
import {
  endOfDay,
  format,
  startOfDay,
  subMinutes,
  isWithinInterval,
} from "date-fns";
import { isValidOrCurrentDate } from "@/lib/utils";

// Separate component for shift items to properly use hooks
const ShiftItem = React.memo(
  ({
    item,
    index,
    onPress,
  }: {
    item: ShiftAgendaItem;
    index: number;
    onPress: (shiftId: string) => void;
  }) => {
    // Calculate if shift is currently accessible
    const now = new Date();
    const shiftStartWithBuffer = subMinutes(item.startDateTime, 30); // 30 minutes before start
    const shiftEnd = item.endDateTime;

    // Check if current time is within the accessible period
    const isShiftAccessible = isWithinInterval(now, {
      start: shiftStartWithBuffer,
      end: shiftEnd,
    });

    // Animation values for entrance effect
    const opacity = useSharedValue(0);
    const translateY = useSharedValue(20);

    React.useEffect(() => {
      const delay = index * 100; // Stagger animation
      opacity.value = withDelay(delay, withTiming(1, { duration: 400 }));
      translateY.value = withDelay(
        delay,
        withSpring(0, { damping: 15, stiffness: 100 })
      );
    }, [index]);

    const animatedStyle = useAnimatedStyle(() => ({
      opacity: opacity.value,
      transform: [{ translateY: translateY.value }],
    }));

    // Determine styles based on accessibility
    const itemStyle = [
      styles.shiftSection,
      !isShiftAccessible && styles.shiftSectionDisabled,
    ];

    const textColor = isShiftAccessible ? Colors.text : Colors.textLight;
    const primaryColor = isShiftAccessible ? Colors.primary : Colors.textLight;

    // Optimized section-based content structure
    const shiftContent = (
      <Animated.View style={[itemStyle, animatedStyle]}>
        {/* Status Indicator */}
        <View
          style={[
            styles.statusIndicator,
            {
              backgroundColor: isShiftAccessible
                ? Colors.primary
                : Colors.textLight,
            },
          ]}
        />

        {/* Compact Main Content */}
        <View style={styles.shiftContent}>
          {/* Compact Header Row */}
          <View style={styles.shiftHeaderRow}>
            <View style={styles.guardInfoCompact}>
              <View style={styles.iconContainerSmall}>
                <Ionicons name="person" size={16} color={primaryColor} />
              </View>
              <Text style={[styles.guardNameCompact, { color: textColor }]}>
                {item.name}
              </Text>
            </View>

            {/* Compact Time Badge */}
            <View
              style={[
                styles.timeBadgeCompact,
                {
                  backgroundColor: isShiftAccessible
                    ? Colors.primary
                    : Colors.lightGray,
                },
              ]}
            >
              <Text
                style={[
                  styles.timeTextCompact,
                  {
                    color: isShiftAccessible ? Colors.white : Colors.textLight,
                  },
                ]}
              >
                {item.startTime} - {item.endTime}
              </Text>
            </View>
          </View>

          {/* Compact Info Row */}
          <View style={styles.infoRow}>
            <View style={styles.locationInfoCompact}>
              <Ionicons name="location" size={14} color={primaryColor} />
              <Text style={[styles.locationTextCompact, { color: textColor }]}>
                {item.location}
              </Text>
            </View>

            <View style={styles.statusRowCompact}>
              <View
                style={[
                  styles.statusBadgeCompact,
                  {
                    backgroundColor: isShiftAccessible
                      ? Colors.success
                      : Colors.lightGray,
                  },
                ]}
              >
                <Text
                  style={[
                    styles.statusTextCompact,
                    {
                      color: isShiftAccessible
                        ? Colors.white
                        : Colors.textLight,
                    },
                  ]}
                >
                  {isShiftAccessible ? "Available" : "Unavailable"}
                </Text>
              </View>

              {isShiftAccessible && (
                <Ionicons
                  name="chevron-forward"
                  size={18}
                  color={Colors.primary}
                  style={styles.chevronIconCompact}
                />
              )}
            </View>
          </View>
        </View>
      </Animated.View>
    );

    if (!isShiftAccessible) {
      // Render disabled shift item (not clickable)
      return shiftContent;
    }

    // Render enabled shift item (clickable)
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => onPress(item.shiftId)}
      >
        {shiftContent}
      </TouchableOpacity>
    );
  }
);

// Simple render function for FlatList
const createRenderShiftItem =
  (onPress: (shiftId: string) => void) =>
  ({ item, index }: { item: ShiftAgendaItem; index: number }) =>
    <ShiftItem item={item} index={index} onPress={onPress} />;

export default function ShiftsScreen() {
  const router = useRouter();
  const [selectedDate, setSelectedDate] = useState(new Date().toDateString());

  // Animation values for header
  const headerOpacity = useSharedValue(0);
  const contentTranslateY = useSharedValue(20);

  React.useEffect(() => {
    headerOpacity.value = withTiming(1, { duration: 400 });
    contentTranslateY.value = withSpring(0, { damping: 15, stiffness: 100 });
  }, []);

  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: contentTranslateY.value }],
  }));

  const markedDates = useMemo(() => {
    return {
      [selectedDate]: {
        selected: true,
        disableTouchEvent: true,
        selectedDotColor: "orange",
      },
    };
  }, [selectedDate]);

  const { session: user } = useSession();

  // TODO: add support for date
  const { data: selectedDayShifts, isLoading: loading } =
    useGetShiftsByUserQuery(
      {
        shiftsInput: {
          userId: user!.userId,
          startDateTime: startOfDay(selectedDate),
          endDateTime: endOfDay(selectedDate),
        },
      },
      { initialData: { getUserShifts: [] } }
    );

  const handleBackPress = () => {
    router.back();
  };

  const handleShiftPress = (shiftId: string) => {
    router.push(`/shifts/${shiftId}`);
  };

  if (loading) return <LoadingScreen />;
  return (
    <View style={styles.container}>
      <StatusBar barStyle="light" />

      {/* Enhanced Header */}
      <Animated.View style={[styles.header, headerAnimatedStyle]}>
        <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
          <Ionicons name="chevron-back" size={28} color={Colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Shift History</Text>
        <View style={styles.headerAccent} />
      </Animated.View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
        </View>
      ) : (
        <Animated.View style={[styles.contentContainer, contentAnimatedStyle]}>
          {/* Enhanced Calendar */}
          <View style={styles.calendarContainer}>
            <Calendar
              onDayPress={(day: DateData) => {
                setSelectedDate(day.dateString);
              }}
              markedDates={markedDates}
              enableSwipeMonths={true}
              theme={{
                calendarBackground: Colors.white,
                textSectionTitleColor: Colors.textSecondary,
                selectedDayBackgroundColor: Colors.primary,
                selectedDayTextColor: Colors.white,
                todayTextColor: Colors.primary,
                dayTextColor: Colors.text,
                textDisabledColor: Colors.textLight,
                dotColor: Colors.primary,
                selectedDotColor: Colors.white,
                arrowColor: Colors.primary,
                monthTextColor: Colors.text,
                indicatorColor: Colors.primary,
                textDayFontWeight: "400",
                textMonthFontWeight: "bold",
                textDayHeaderFontWeight: "500",
              }}
            />
          </View>

          {/* Enhanced Shifts Section */}
          <View style={styles.shiftsContainer}>
            <View style={styles.sectionHeader}>
              <View style={styles.sectionTitleContainer}>
                <Ionicons name="calendar" size={20} color={Colors.primary} />
                <Text style={styles.sectionTitle}>
                  Shifts for{" "}
                  <Text style={styles.selectedDate}>
                    {format(isValidOrCurrentDate(selectedDate), "dd MMM yyyy")}
                  </Text>
                </Text>
              </View>

              {selectedDayShifts!.getUserShifts!.length > 0 && (
                <View style={styles.shiftsCount}>
                  <Text style={styles.shiftsCountText}>
                    {selectedDayShifts!.getUserShifts!.length}
                  </Text>
                </View>
              )}
            </View>

            {selectedDayShifts!.getUserShifts!.length > 0 ? (
              <FlatList
                data={selectedDayShifts!.getUserShifts.map((shift) =>
                  shiftAgendaItemMapper(shift)
                )}
                renderItem={createRenderShiftItem(handleShiftPress)}
                keyExtractor={(item) => item.shiftId}
                contentContainerStyle={styles.shiftsList}
                showsVerticalScrollIndicator={false}
              />
            ) : (
              <View style={styles.emptyShifts}>
                <Ionicons
                  name="calendar-outline"
                  size={48}
                  color={Colors.textLight}
                />
                <Text style={styles.emptyShiftsText}>
                  No shifts scheduled for this day
                </Text>
                <Text style={styles.emptyShiftsSubtext}>
                  Select a different date to view available shifts
                </Text>
              </View>
            )}
          </View>
        </Animated.View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  // Enhanced Header Styles
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
    position: "relative",
  },
  headerAccent: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 3,
    backgroundColor: Colors.primaryLight,
  },
  backButton: {
    marginRight: 16,
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.white,
    flex: 1,
  },

  // Enhanced Content Styles
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  contentContainer: {
    flex: 1,
  },
  calendarContainer: {
    backgroundColor: Colors.white,
    elevation: 2,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },

  // Optimized Compact Shifts Section
  shiftsContainer: {
    flex: 1,
    padding: 12,
    backgroundColor: Colors.background,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  sectionTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: "700",
    color: Colors.text,
    marginLeft: 8,
  },
  shiftsCount: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    minWidth: 28,
    alignItems: "center",
  },
  shiftsCountText: {
    color: Colors.white,
    fontSize: 13,
    fontWeight: "700",
  },
  shiftsList: {
    paddingBottom: 12,
  },

  // Optimized Compact Shift Item Styles (Section-based)
  shiftSection: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    marginBottom: 8,
    overflow: "hidden",
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  shiftSectionDisabled: {
    backgroundColor: Colors.lightGray,
    opacity: 0.7,
  },
  statusIndicator: {
    width: 3,
    height: "100%",
    position: "absolute",
    left: 0,
    top: 0,
    bottom: 0,
  },
  shiftContent: {
    padding: 12,
    paddingLeft: 16, // Account for status indicator
  },

  // Compact Header Row
  shiftHeaderRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  guardInfoCompact: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  iconContainerSmall: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.background,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 8,
  },
  guardNameCompact: {
    fontSize: 15,
    fontWeight: "600",
    color: Colors.text,
    flex: 1,
  },
  timeBadgeCompact: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    backgroundColor: Colors.primary,
  },
  timeTextCompact: {
    fontSize: 11,
    fontWeight: "600",
    color: Colors.white,
  },

  // Compact Info Row
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  locationInfoCompact: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    marginRight: 12,
  },
  locationTextCompact: {
    fontSize: 13,
    fontWeight: "500",
    color: Colors.text,
    marginLeft: 6,
  },
  statusRowCompact: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusBadgeCompact: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 8,
    backgroundColor: Colors.success,
  },
  statusTextCompact: {
    fontSize: 11,
    fontWeight: "600",
    color: Colors.white,
  },
  chevronIconCompact: {
    marginLeft: 6,
  },

  // Legacy styles (keeping for compatibility)
  shiftHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  guardInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.background,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  guardDetails: {
    flex: 1,
  },
  guardLabel: {
    fontSize: 12,
    fontWeight: "600",
    color: Colors.textSecondary,
    textTransform: "uppercase",
    letterSpacing: 0.5,
    marginBottom: 2,
  },
  guardName: {
    fontSize: 16,
    fontWeight: "700",
    color: Colors.text,
  },
  timeBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    backgroundColor: Colors.primary,
  },
  timeText: {
    fontSize: 12,
    fontWeight: "600",
    color: Colors.white,
    marginLeft: 4,
  },
  locationInfo: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  locationText: {
    fontSize: 14,
    fontWeight: "500",
    color: Colors.text,
  },
  statusSection: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: Colors.success,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "600",
    color: Colors.white,
  },
  chevronIcon: {
    marginLeft: 8,
  },

  // Selected Date Styling
  selectedDate: {
    fontSize: 18,
    fontWeight: "800",
    color: Colors.primary,
  },

  // Enhanced Empty State
  emptyShifts: {
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.white,
    borderRadius: 16,
    padding: 32,
    marginTop: 16,
    borderWidth: 1,
    borderColor: Colors.border,
    borderStyle: "dashed",
  },
  emptyShiftsText: {
    color: Colors.textSecondary,
    fontSize: 16,
    fontWeight: "600",
    marginTop: 12,
    textAlign: "center",
  },
  emptyShiftsSubtext: {
    color: Colors.textLight,
    fontSize: 14,
    marginTop: 4,
    textAlign: "center",
  },

  // Legacy styles (keeping for compatibility)
  shiftItemContent: {
    flexDirection: "row",
    alignItems: "flex-start",
    width: "100%",
  },
  shiftTimeContainer: {
    alignItems: "flex-end",
    justifyContent: "flex-start",
    marginLeft: 12,
  },
  assignedSection: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 6,
  },
  locationSection: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  timeSection: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
  },
  sectionIcon: {
    marginRight: 6,
  },
  timeIcon: {
    marginRight: 4,
  },
  shiftTime: {
    fontSize: 13,
    color: Colors.textSecondary,
    fontWeight: "600",
  },
  shiftDetails: {
    flex: 1,
    paddingRight: 8,
  },
  shiftName: {
    fontSize: 17,
    fontWeight: "600",
    color: Colors.text,
    marginBottom: 2,
    lineHeight: 22,
  },
  assignedGuards: {
    fontSize: 12,
    fontWeight: "600",
    color: Colors.textSecondary,
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  shiftLocation: {
    fontSize: 14,
    color: Colors.textSecondary,
    fontWeight: "500",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginTop: 16,
    marginBottom: 24,
  },
  emptyButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: "500",
  },
});
