import { z } from "zod";

// Schema for a single allowance entry
export const allowanceEntrySchema = z.object({
  date: z.string().min(1, { message: "Date is required" }),
  day: z.string().min(1, { message: "Day is required" }),
  jobNo: z.string().min(1, { message: "Job number is required" }),
  reason: z.string().min(1, { message: "Reason is required" }),
  workingHours: z.string().min(1, { message: "Working hours is required" }),
  amount: z.string().min(1, { message: "Amount is required" }),
  total: z.string().min(1, { message: "Total is required" }),
  invoice: z.array(z.string()).nullable(),
});

// Schema for the entire allowances form
export const allowancesSchema = z.object({
  allowances: z
    .array(allowanceEntrySchema)
    .min(1, { message: "At least one allowance entry is required" }),
});

// Type for a single allowance entry
export type AllowanceEntry = z.infer<typeof allowanceEntrySchema>;

// Type for the entire allowances form
export type AllowancesFormData = z.infer<typeof allowancesSchema>;

// Default values for a single allowance entry
export const defaultAllowanceEntry: AllowanceEntry = {
  date: "",
  day: "",
  jobNo: "",
  reason: "",
  workingHours: "",
  amount: "",
  total: "",
  invoice: null,
};

// Default values for the entire allowances form
export const defaultAllowancesValues: AllowancesFormData = {
  allowances: [defaultAllowanceEntry],
};
